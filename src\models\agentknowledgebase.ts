import { <PERSON>ed<PERSON><PERSON><PERSON>, SWRConfig, SWRConfiguration } from "swr";
import { IBasemodel, IBasemodelController, ListRequest, ListResponse } from "./base-model";
import { BaseModelImplementation } from "./base-implementation";
import { APIRoutes } from "@/constants";
import { Fetcher2 } from "@/functions/fetcher2";


export enum AgentKnowledgebaseStatus {
    ASSIGNING = 'ASSIGNING',
    READY = 'READY'
}

export interface AgentKnowledgebase extends IBasemodel {
    agentId: string;
    agentName: string;
    knowledgebaseId: string;
    knowledgeBaseName: string;
    status: AgentKnowledgebaseStatus;
}

export interface AssignAgentKnowledgebaseRequest {
    agentId: string;
    kbId: string;
    description: string;
}

export interface UnassignAgentKnowledgebaseRequest {
    agentId: string;
    kbId: string;
}

export enum AgentKnowledgebaseOperation {
    ASSIGN = 'ASSIGN',
    UNASSIGN = 'UNASSIGN'
}

export interface AgentKnowledgebaseResponse {
    operation: AgentKnowledgebaseOperation;
    agentId: string;
    kbId: string;
}

export interface AgentKnowledgebaseLinkExists {
    exists: boolean;
}

export class AgentKnowledgebaseController {
    assignAgentKnowledgebase = () => Fetcher2.SWRMutationTemplate(APIRoutes.AgentKnowledgebaseController.ASSIGN, { method: 'POST' }, {});
    unassignAgentKnowledgebase = () => Fetcher2.SWRMutationTemplate(APIRoutes.AgentKnowledgebaseController.UNASSIGN, { method: 'POST' }, {});
    existsAgentKnowledgebase = (kbId: string, agentId?: string) => {
        return Fetcher2.SWRTemplate<AgentKnowledgebaseLinkExists>(
            APIRoutes.AgentKnowledgebaseController.EXISTS,
            { method: 'GET', queryString: { kbId: kbId, agentId: agentId } },
            {
                isPaused: () => (agentId===undefined || agentId == null || agentId.length == 0)
            }
        )
    }
}