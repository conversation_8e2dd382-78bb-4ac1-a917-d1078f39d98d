import { AgentKnowledgebaseStatus } from "@/models/agentknowledgebase"
import { Tag } from "antd"
import {
    CheckCircleOutlined,
    SyncOutlined
} from '@ant-design/icons';

export function GetAgentKnowledgebaseStatusTag(status: AgentKnowledgebaseStatus) {
    switch (status) {
        case AgentKnowledgebaseStatus.ASSIGNING:
            return (<Tag icon={<SyncOutlined spin />} color="purple">assigning</Tag>)
        case AgentKnowledgebaseStatus.READY:
            return (<Tag icon={<CheckCircleOutlined />} color="success">ready</Tag>)
        default:
            return (<Tag color="default">unknown</Tag>)
    }
}